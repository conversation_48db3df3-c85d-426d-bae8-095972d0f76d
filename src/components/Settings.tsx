import { Settings as Settings<PERSON><PERSON>, <PERSON>, Sun, LogOut, Trash2, Activity, Award, Palette, User, Mail, Link, X } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "./ui/dialog";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { useState, useEffect } from "react";
import { useToast } from "./ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import { useTheme } from "@/hooks/use-theme";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useSupabaseUserStore } from "@/stores/supabaseUserStore";
import { Avatar, AvatarImage, AvatarFallback } from "./ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";
import { Loader2 } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Progress } from "./ui/progress";
import { Separator } from "./ui/separator";
import { useLocation, useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

export const Settings = () => {
  const { user, signOut } = useSupabaseAuth();
  const { userProfile } = useSupabaseUserStore();
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();

  // Check if current route is a landing page
  const isLandingPage = location.pathname.includes('-landing') || location.pathname === '/';

  const handleDeleteChats = async () => {
    if (!user) return;

    try {
      // Clear localStorage
      localStorage.removeItem("chatHistoryList");
      localStorage.removeItem("currentChatId");

      toast({
        title: "Chats Deleted",
        description: "All chat history has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting all chats:', error);
      toast({
        title: "Error",
        description: "Failed to delete all chats. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const navigateToSettings = () => {
    navigate('/settings');
  };

  if (!user || isLandingPage) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 right-4 z-[35] rounded-full"
        >
          <Avatar className="h-9 w-9">
            <AvatarImage
              src={userProfile?.photo_url || user.user_metadata?.avatar_url || undefined}
              alt={userProfile?.display_name || user.user_metadata?.full_name || "User"}
            />
            <AvatarFallback>
              {userProfile?.display_name?.[0]?.toUpperCase() ||
               user.user_metadata?.full_name?.[0]?.toUpperCase() ||
               user.email?.[0]?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem className="flex items-center gap-2" onClick={() => navigate('/profile')}>
          <User className="h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="flex items-center gap-2" onClick={navigateToSettings}>
          <SettingsIcon className="h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="flex items-center gap-2" onClick={handleSignOut}>
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
