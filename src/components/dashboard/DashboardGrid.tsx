import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from '../ui/card';
import { 
  CheckSquare, 
  Calendar, 
  BarChart3, 
  Brain, 
  Trophy, 
  Flame, 
  Timer, 
  PieChart 
} from 'lucide-react';

interface DashboardGridProps {
  userId: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ userId }) => {
  // Placeholder widgets - these will be replaced with actual widget components in later tasks
  const widgets = [
    {
      id: 'tasks',
      title: "Today's Tasks",
      icon: CheckSquare,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50 dark:bg-orange-900/10',
      borderColor: 'border-l-orange-500',
      content: 'Task management widget will be implemented in task 4'
    },
    {
      id: 'exams',
      title: 'Upcoming Exams',
      icon: Calendar,
      color: 'text-rose-500',
      bgColor: 'bg-rose-50 dark:bg-rose-900/10',
      borderColor: 'border-l-rose-500',
      content: 'Exam countdown widget will be implemented in task 5'
    },
    {
      id: 'progress',
      title: 'Chapter Progress',
      icon: BarChart3,
      color: 'text-emerald-500',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/10',
      borderColor: 'border-l-emerald-500',
      content: 'Progress tracking widget will be implemented in task 7'
    },
    {
      id: 'swot',
      title: 'SWOT Analysis',
      icon: Brain,
      color: 'text-violet-500',
      bgColor: 'bg-violet-50 dark:bg-violet-900/10',
      borderColor: 'border-l-violet-500',
      content: 'AI-powered SWOT widget will be implemented in task 8'
    },
    {
      id: 'leaderboard',
      title: 'Productivity Leaderboard',
      icon: Trophy,
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/10',
      borderColor: 'border-l-indigo-500',
      content: 'Leaderboard widget will be implemented in task 9'
    },
    {
      id: 'streak',
      title: 'Study Streak',
      icon: Flame,
      color: 'text-teal-500',
      bgColor: 'bg-teal-50 dark:bg-teal-900/10',
      borderColor: 'border-l-teal-500',
      content: 'Streak tracking widget will be implemented in task 10'
    },
    {
      id: 'countdown',
      title: 'D-Day Countdown',
      icon: Timer,
      color: 'text-pink-500',
      bgColor: 'bg-pink-50 dark:bg-pink-900/10',
      borderColor: 'border-l-pink-500',
      content: 'Countdown widget will be implemented in task 6'
    },
    {
      id: 'analytics',
      title: 'Analytics Overview',
      icon: PieChart,
      color: 'text-slate-500',
      bgColor: 'bg-slate-50 dark:bg-slate-900/10',
      borderColor: 'border-l-slate-500',
      content: 'Analytics widget will be implemented in task 12'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Welcome to your Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Here's an overview of your academic progress and activities
        </p>
      </div>

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {widgets.map((widget) => {
          const Icon = widget.icon;
          
          return (
            <Card 
              key={widget.id}
              className={`${widget.bgColor} border-l-4 ${widget.borderColor} hover:shadow-md transition-shadow duration-200`}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Icon className={`h-5 w-5 ${widget.color}`} />
                  <span className="text-gray-800 dark:text-gray-200">
                    {widget.title}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600 dark:text-gray-400 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                  {widget.content}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats Section */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Study Time Today
                </p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  2h 45m
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <Timer className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                  Tasks Completed
                </p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  8/12
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <CheckSquare className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  Current Streak
                </p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  7 days
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <Flame className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardGrid;